version: '3.8'

services:
  # Payroll MCP Server
  payroll-mcp:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: payroll-mcp-server
    restart: unless-stopped
    ports:
      - "${PORT:-3000}:3000"
    environment:
      # Server Configuration
      - NODE_ENV=production
      - PORT=3000
      - LOG_LEVEL=${LOG_LEVEL:-info}

      # External Service Authentication
      - EXTERNAL_SERVICE_ACCESS_TOKEN=${EXTERNAL_SERVICE_ACCESS_TOKEN}

      # External API Configuration
      - EXTERNAL_API_URL=${EXTERNAL_API_URL}
      - API_TIMEOUT=${API_TIMEOUT:-30000}
      - API_RETRIES=${API_RETRIES:-3}
      - API_RETRY_DELAY=${API_RETRY_DELAY:-1000}

      # Security Configuration
      - ENABLE_DNS_REBINDING_PROTECTION=${ENABLE_DNS_REBINDING_PROTECTION:-true}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-127.0.0.1,localhost}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
      - RATE_LIMIT_MAX=${RATE_LIMIT_MAX:-100}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - payroll-network
    healthcheck:
      test: ["CMD", "node", "-e", "const http = require('http'); const options = { hostname: 'localhost', port: 3000, path: '/health', timeout: 5000 }; const req = http.request(options, (res) => { if (res.statusCode === 200) { process.exit(0); } else { process.exit(1); } }); req.on('error', () => process.exit(1)); req.on('timeout', () => process.exit(1)); req.end();"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s



  # Nginx (Optional - reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: payroll-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - payroll-network
    depends_on:
      - payroll-mcp
    profiles:
      - production

volumes:
  # No volumes needed for current configuration

networks:
  payroll-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
