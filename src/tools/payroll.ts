import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { PayrollApiService } from '../services/payroll-api.js';
import { Logger } from '../utils/logger.js';
import { PayrollParametersSchema } from '../types/payroll.js';
import type { PayrollParameters } from '../types/payroll.js';

// Extended schema with pagination and sorting options
export const PayrollToolInputSchema = z.object({
  // Core payroll parameters
  ...PayrollParametersSchema.shape,

  // Pagination parameters
  page: z.number().int().min(1).default(1).describe('Page number for pagination (starts from 1)'),
  rows: z.number().int().min(1).max(1000).default(30).describe('Number of rows per page (max 1000)'),

  // Sorting parameters
  sort: z.string().default('owner_names').describe('Field to sort by (e.g., owner_names, area, renta)'),
  order: z.enum(['asc', 'desc']).default('asc').describe('Sort order: ascending or descending'),

  // Output format options
  include_footer: z.boolean().default(true).describe('Include summary footer in response'),
  include_children: z.boolean().default(true).describe('Include heritor children records'),
  format: z.enum(['full', 'summary', 'minimal']).default('full').describe('Response detail level')
});

export type PayrollToolInput = z.infer<typeof PayrollToolInputSchema>;

export class PayrollTool {
  private apiService: PayrollApiService;
  private logger: Logger;

  constructor(apiService: PayrollApiService, logger: Logger) {
    this.apiService = apiService;
    this.logger = logger;
  }

  /**
   * Execute the payroll tool with the given parameters
   */
  async execute(input: PayrollToolInput, accessToken: string): Promise<any> {
    const startTime = Date.now();

    try {
      this.logger.tool('Executing payroll tool', {
        type: input.type,
        farming_year: input.farming_year,
        page: input.page,
        rows: input.rows,
        sort: input.sort,
        order: input.order,
        format: input.format
      });

      // Validate input parameters
      const validatedInput = this.validateInput(input);

      // Extract payroll parameters (excluding pagination and formatting options)
      const payrollParams = this.extractPayrollParameters(validatedInput);

      // Call the external API
      const apiResponse = await this.apiService.getPayrollData(
        payrollParams,
        accessToken,
        validatedInput.page,
        validatedInput.rows,
        validatedInput.sort,
        validatedInput.order
      );

      // Format the response based on the requested format
      const formattedResponse = this.formatResponse(apiResponse, validatedInput);

      const duration = Date.now() - startTime;
      this.logger.toolExecution('payroll', validatedInput, true, duration);

      return {
        content: [
          {
            type: 'text',
            text: this.generateTextSummary(apiResponse, validatedInput)
          }
        ],
        structuredContent: formattedResponse
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.logger.toolExecution('payroll', input, false, duration);
      this.logger.error('Payroll tool execution failed', {
        error: errorMessage,
        input: this.sanitizeInput(input)
      });

      return {
        content: [
          {
            type: 'text',
            text: `Error executing payroll tool: ${errorMessage}`
          }
        ],
        isError: true
      };
    }
  }

  /**
   * Validate and sanitize input parameters
   */
  private validateInput(input: PayrollToolInput): PayrollToolInput {
    try {
      return PayrollToolInputSchema.parse(input);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(issue => `${issue.path.join('.')}: ${issue.message}`).join(', ');
        throw new Error(`Invalid parameters: ${issues}`);
      }
      throw error;
    }
  }

  /**
   * Extract payroll parameters from the full input
   */
  private extractPayrollParameters(input: PayrollToolInput): PayrollParameters {
    const {
      page, rows, sort, order, include_footer, include_children, format,
      ...payrollParams
    } = input;

    return payrollParams;
  }

  /**
   * Format the API response based on the requested format
   */
  private formatResponse(apiResponse: any, input: PayrollToolInput): any {
    const { format, include_footer, include_children } = input;

    let formattedResponse = {
      rows: apiResponse.rows || [],
      total: apiResponse.total || 0,
      pagination: {
        page: input.page,
        rows: input.rows,
        total_pages: Math.ceil((apiResponse.total || 0) / input.rows),
        has_more: (apiResponse.total || 0) > (input.page * input.rows)
      }
    };

    // Include footer if requested
    if (include_footer && apiResponse.footer) {
      (formattedResponse as any).footer = (apiResponse as any).footer;
    }

    // Process children records if not requested
    if (!include_children) {
      formattedResponse.rows = formattedResponse.rows.map((row: any) => {
        const { children, ...rowWithoutChildren } = row;
        return rowWithoutChildren;
      });
    }

    // Apply format-specific transformations
    switch (format) {
      case 'minimal':
        return this.formatMinimal(formattedResponse);
      case 'summary':
        return this.formatSummary(formattedResponse);
      case 'full':
      default:
        return formattedResponse;
    }
  }

  /**
   * Format response for minimal output
   */
  private formatMinimal(response: any): any {
    return {
      total: response.total,
      pagination: response.pagination,
      rows: response.rows.map((row: any) => ({
        id: row.id,
        owner_id: row.owner_id,
        owner_names: row.owner_names,
        egn_eik: row.egn_eik,
        area: row.area,
        renta: row.renta,
        paid_renta: row.paid_renta,
        unpaid_renta: row.unpaid_renta
      }))
    };
  }

  /**
   * Format response for summary output
   */
  private formatSummary(response: any): any {
    return {
      total: response.total,
      pagination: response.pagination,
      summary: response.footer?.[0] || {},
      rows: response.rows.map((row: any) => ({
        id: row.id,
        owner_id: row.owner_id,
        owner_names: row.owner_names,
        egn_eik: row.egn_eik,
        owner_type: row.owner_type,
        area: row.area,
        cultivated_area: row.cultivated_area,
        renta: row.renta,
        charged_renta: row.charged_renta,
        paid_renta: row.paid_renta,
        unpaid_renta: row.unpaid_renta,
        over_paid: row.over_paid,
        is_dead: row.is_dead,
        is_heritor: row.is_heritor,
        children_count: row.children?.length || 0
      }))
    };
  }

  /**
   * Generate a human-readable text summary
   */
  private generateTextSummary(apiResponse: any, input: PayrollToolInput): string {
    const { rows = [], total = 0, footer = [] } = apiResponse;
    const { type, farming_year, page, rows: pageSize } = input;

    let summary = `Payroll Data Summary\n`;
    summary += `====================\n\n`;
    summary += `Query Type: ${type}\n`;
    summary += `Farming Year: ${farming_year}\n`;
    summary += `Total Records: ${total}\n`;
    summary += `Page: ${page} (showing ${rows.length} of ${pageSize} requested)\n\n`;

    if (rows.length > 0) {
      summary += `Records:\n`;
      summary += `--------\n`;

      rows.slice(0, 5).forEach((row: any, index: number) => {
        summary += `${index + 1}. ${row.owner_names || 'Unknown'} (${row.egn_eik || 'N/A'})\n`;
        summary += `   Area: ${row.area || '0'} ha, Rent: ${row.renta || '0'} BGN\n`;
        summary += `   Paid: ${row.paid_renta || '0'} BGN, Unpaid: ${row.unpaid_renta || '0'} BGN\n`;
        if (row.is_heritor) summary += `   (Heritor record)\n`;
        if (row.is_dead) summary += `   (Deceased owner)\n`;
        summary += `\n`;
      });

      if (rows.length > 5) {
        summary += `... and ${rows.length - 5} more records\n\n`;
      }
    }

    // Add footer summary if available
    if (footer.length > 0 && footer[0]) {
      const footerData = footer[0];
      summary += `Totals:\n`;
      summary += `-------\n`;
      if (footerData.all_owner_area) summary += `Total Area: ${footerData.all_owner_area} ha\n`;
      if (footerData.renta) summary += `Total Rent: ${footerData.renta} BGN\n`;
      if (footerData.paid_renta) summary += `Total Paid: ${footerData.paid_renta} BGN\n`;
      if (footerData.unpaid_renta) summary += `Total Unpaid: ${footerData.unpaid_renta} BGN\n`;
    }

    return summary;
  }

  /**
   * Sanitize input for logging (remove sensitive data)
   */
  private sanitizeInput(input: PayrollToolInput): Partial<PayrollToolInput> {
    // For payroll data, most fields are safe to log
    // Remove any potentially sensitive fields if needed
    return { ...input };
  }

  /**
   * Get tool schema for MCP registration
   */
  static getToolSchema() {
    return {
      name: 'payroll',
      description: 'Retrieve payroll data for owners, heritors, and their associated contracts. Supports filtering by various criteria including owner information, location, dates, and more.',
      inputSchema: zodToJsonSchema(PayrollToolInputSchema, 'PayrollToolInput')
    };
  }

  /**
   * Validate tool parameters without executing
   */
  static validateParameters(input: unknown): { valid: boolean; errors?: string[] } {
    try {
      PayrollToolInputSchema.parse(input);
      return { valid: true };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.issues.map(issue => `${issue.path.join('.')}: ${issue.message}`);
        return { valid: false, errors };
      }
      return { valid: false, errors: ['Unknown validation error'] };
    }
  }
}
